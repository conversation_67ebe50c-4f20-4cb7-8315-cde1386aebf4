import { Phase0, WebAppHeader } from "@/components/webApp/phases";
import type { WelcomeData } from "@/components/webApp/phases/Phase0";
import { useState } from "react";

export function WebApp() {
  const [phase, setPhase] = useState(0);
  const [welcomeFormSchema, setWelcomeFormSchema] = useState<WelcomeData>();

  const totalPhases = 5;

  const handleBack = () => {
    if (phase > 0) {
      setPhase(phase - 1);
    }
  };

  const handleNext = () => {
    if (phase < totalPhases - 1) {
      setPhase(phase + 1);
    }
  };

  const renderPhase = () => {
    switch (phase) {
      case 0:
        return (
          <Phase0
            onNext={handleNext}
            formData={welcomeFormSchema}
            setFormData={setWelcomeFormSchema}
          />
        );
      default:
        return <div className=""></div>;
    }
  };

  return (
    <div className="relative min-h-screen overflow-x-hidden theme-transition">
      {/* Header */}
      <WebAppHeader
        currentPhase={phase}
        totalPhases={totalPhases}
        onBack={handleBack}
        canGoBack={phase > 0}
      />

      {/* Main Content */}
      <main className="relative z-10 pt-20 sm:pt-24 md:pt-28 lg:pt-32 px-4 sm:px-6 lg:px-8 pb-8">
        <div className="max-w-4xl mx-auto">{renderPhase()}</div>
      </main>
    </div>
  );
}
