import { z } from "zod";
import { useZodForm } from "@/hooks/useZodForm";

interface Phase1Props {
  onNext: () => void;
  onBack: () => void;
  formData?: BusinessData;
  setFormData: (data: BusinessData) => void;
}

const businessSchema = z.object({
  industry: z.string().min(1, "Industry is required"),
  companySize: z.enum(["1-10", "11-50", "51-200", "201-1000", "1000+"], {
    errorMap: () => ({ message: "Please select company size" }),
  }),
  currentChallenges: z
    .array(z.string())
    .min(1, "Please select at least one challenge"),
  monthlyRevenue: z.enum([
    "0-10k",
    "10k-50k",
    "50k-100k",
    "100k-500k",
    "500k+",
  ], {
    errorMap: () => ({ message: "Please select monthly revenue range" }),
  }),
  currentTools: z.string().optional(),
  painPoints: z.string().min(10, "Please describe your main pain points (minimum 10 characters)"),
});

export type BusinessData = z.infer<typeof businessSchema>;

const industries = [
  "Technology",
  "Healthcare",
  "Finance",
  "Retail",
  "Manufacturing",
  "Education",
  "Real Estate",
  "Marketing/Advertising",
  "Consulting",
  "E-commerce",
  "Other",
];

const challenges = [
  "Manual repetitive tasks",
  "Customer service efficiency",
  "Data analysis and reporting",
  "Lead generation and qualification",
  "Content creation",
  "Inventory management",
  "Employee productivity",
  "Process automation",
  "Decision making",
  "Communication workflows",
];

export function Phase1({ onNext, onBack, formData, setFormData }: Phase1Props) {
  const form = useZodForm({
    schema: businessSchema,
    initialValues: {
      industry: formData?.industry || "",
      companySize: formData?.companySize || ("" as any),
      currentChallenges: formData?.currentChallenges || [],
      monthlyRevenue: formData?.monthlyRevenue || ("" as any),
      currentTools: formData?.currentTools || "",
      painPoints: formData?.painPoints || "",
    },
    onSubmit: async (data) => {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Save form data and proceed to next phase
      setFormData(data);
      onNext();
    },
  });

  const handleChallengeToggle = (challenge: string) => {
    const currentChallenges = form.values.currentChallenges;
    const newChallenges = currentChallenges.includes(challenge)
      ? currentChallenges.filter((c) => c !== challenge)
      : [...currentChallenges, challenge];
    
    form.setValue("currentChallenges", newChallenges);
  };

  return (
    <div className="w-full max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="space-y-8 sm:space-y-12">
        {/* Main Headline */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-title font-bold text-dark dark:text-white theme-transition leading-tight">
            Tell us about your business
          </h1>
          <p className="text-lg sm:text-xl text-gray dark:text-gray theme-transition max-w-2xl mx-auto">
            Help us understand your current situation so we can provide the most relevant AI solutions
          </p>
        </div>

        {/* Form Section */}
        <div className="max-w-2xl mx-auto">
          <form onSubmit={form.handleSubmit} className="space-y-6">
            {/* Industry */}
            <div className="space-y-2">
              <label 
                htmlFor="industry" 
                className="block text-sm font-semibold text-dark dark:text-light theme-transition"
              >
                Industry *
              </label>
              <select
                id="industry"
                value={form.values.industry}
                onChange={(e) => form.setValue("industry", e.target.value)}
                className={`w-full px-4 py-3 rounded-lg border-2 bg-white/80 dark:bg-gray/20 text-dark dark:text-white font-body theme-transition focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 transition-all duration-200 ${
                  form.errors.industry 
                    ? "border-red-500 focus:border-red-500" 
                    : "border-gray/30 dark:border-light/30 focus:border-dark dark:focus:border-light"
                }`}
              >
                <option value="">Select your industry</option>
                {industries.map((industry) => (
                  <option key={industry} value={industry}>
                    {industry}
                  </option>
                ))}
              </select>
              {form.errors.industry && (
                <p className="text-red-500 text-sm font-body">{form.errors.industry}</p>
              )}
            </div>

            {/* Company Size */}
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-dark dark:text-light theme-transition">
                Company Size *
              </label>
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                {["1-10", "11-50", "51-200", "201-1000", "1000+"].map((size) => (
                  <button
                    key={size}
                    type="button"
                    onClick={() => form.setValue("companySize", size as any)}
                    className={`px-4 py-3 rounded-lg border-2 font-body transition-all duration-200 ${
                      form.values.companySize === size
                        ? "bg-dark dark:bg-light text-white dark:text-dark border-dark dark:border-light"
                        : "bg-white/80 dark:bg-gray/20 text-dark dark:text-white border-gray/30 dark:border-light/30 hover:border-dark dark:hover:border-light"
                    }`}
                  >
                    {size} employees
                  </button>
                ))}
              </div>
              {form.errors.companySize && (
                <p className="text-red-500 text-sm font-body">{form.errors.companySize}</p>
              )}
            </div>

            {/* Current Challenges */}
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-dark dark:text-light theme-transition">
                Current Business Challenges * (Select all that apply)
              </label>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {challenges.map((challenge) => (
                  <button
                    key={challenge}
                    type="button"
                    onClick={() => handleChallengeToggle(challenge)}
                    className={`px-4 py-3 rounded-lg border-2 font-body transition-all duration-200 text-left ${
                      form.values.currentChallenges.includes(challenge)
                        ? "bg-dark dark:bg-light text-white dark:text-dark border-dark dark:border-light"
                        : "bg-white/80 dark:bg-gray/20 text-dark dark:text-white border-gray/30 dark:border-light/30 hover:border-dark dark:hover:border-light"
                    }`}
                  >
                    {challenge}
                  </button>
                ))}
              </div>
              {form.errors.currentChallenges && (
                <p className="text-red-500 text-sm font-body">{form.errors.currentChallenges}</p>
              )}
            </div>

            {/* Monthly Revenue */}
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-dark dark:text-light theme-transition">
                Monthly Revenue *
              </label>
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                {["0-10k", "10k-50k", "50k-100k", "100k-500k", "500k+"].map((revenue) => (
                  <button
                    key={revenue}
                    type="button"
                    onClick={() => form.setValue("monthlyRevenue", revenue as any)}
                    className={`px-4 py-3 rounded-lg border-2 font-body transition-all duration-200 ${
                      form.values.monthlyRevenue === revenue
                        ? "bg-dark dark:bg-light text-white dark:text-dark border-dark dark:border-light"
                        : "bg-white/80 dark:bg-gray/20 text-dark dark:text-white border-gray/30 dark:border-light/30 hover:border-dark dark:hover:border-light"
                    }`}
                  >
                    ${revenue}
                  </button>
                ))}
              </div>
              {form.errors.monthlyRevenue && (
                <p className="text-red-500 text-sm font-body">{form.errors.monthlyRevenue}</p>
              )}
            </div>

            {/* Current Tools */}
            <div className="space-y-2">
              <label 
                htmlFor="currentTools" 
                className="block text-sm font-semibold text-dark dark:text-light theme-transition"
              >
                Current Tools & Software (Optional)
              </label>
              <textarea
                id="currentTools"
                value={form.values.currentTools || ""}
                onChange={(e) => form.setValue("currentTools", e.target.value)}
                rows={3}
                className="w-full px-4 py-3 rounded-lg border-2 bg-white/80 dark:bg-gray/20 text-dark dark:text-white font-body theme-transition focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 transition-all duration-200 border-gray/30 dark:border-light/30 focus:border-dark dark:focus:border-light resize-none"
                placeholder="List the main tools and software your business currently uses..."
              />
            </div>

            {/* Pain Points */}
            <div className="space-y-2">
              <label 
                htmlFor="painPoints" 
                className="block text-sm font-semibold text-dark dark:text-light theme-transition"
              >
                Main Pain Points *
              </label>
              <textarea
                id="painPoints"
                value={form.values.painPoints}
                onChange={(e) => form.setValue("painPoints", e.target.value)}
                rows={4}
                className={`w-full px-4 py-3 rounded-lg border-2 bg-white/80 dark:bg-gray/20 text-dark dark:text-white font-body theme-transition focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 transition-all duration-200 resize-none ${
                  form.errors.painPoints 
                    ? "border-red-500 focus:border-red-500" 
                    : "border-gray/30 dark:border-light/30 focus:border-dark dark:focus:border-light"
                }`}
                placeholder="Describe the biggest challenges your business faces that you'd like AI to help solve..."
              />
              {form.errors.painPoints && (
                <p className="text-red-500 text-sm font-body">{form.errors.painPoints}</p>
              )}
            </div>

            {/* Navigation Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 pt-6">
              <button
                type="button"
                onClick={onBack}
                className="px-8 py-3 bg-white/10 dark:bg-gray/20 text-dark dark:text-light border-2 border-dark/20 dark:border-light/20 rounded-full font-body font-semibold transition-all duration-300 hover:bg-white/20 dark:hover:bg-gray/30 hover:border-dark/40 dark:hover:border-light/40 focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50"
              >
                Back
              </button>
              <button
                type="submit"
                disabled={form.isSubmitting}
                className={`flex-1 px-8 py-3 bg-dark dark:bg-light text-white dark:text-dark rounded-full font-body font-bold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl theme-transition focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 ${
                  form.isSubmitting ? 'hover:bg-dark dark:hover:bg-light' : 'hover:bg-gray-800 dark:hover:bg-gray-200'
                }`}
              >
                {form.isSubmitting ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    <span>Processing...</span>
                  </div>
                ) : (
                  "Continue"
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
