import { z } from "zod";
import { useZodForm } from "@/hooks/useZodForm";

interface Phase2Props {
  onNext: () => void;
  onBack: () => void;
  formData?: AIInterestsData;
  setFormData: (data: AIInterestsData) => void;
}

const aiInterestsSchema = z.object({
  aiInterests: z
    .array(z.string())
    .min(1, "Please select at least one AI interest"),
  currentAiUsage: z.enum(["none", "basic", "intermediate", "advanced"], {
    errorMap: () => ({ message: "Please select current AI usage level" }),
  }),
  budget: z.enum([
    "under-5k",
    "5k-15k",
    "15k-50k",
    "50k-100k",
    "100k+",
  ], {
    errorMap: () => ({ message: "Please select budget range" }),
  }),
  timeline: z.enum(["immediate", "1-3months", "3-6months", "6months+"], {
    errorMap: () => ({ message: "Please select implementation timeline" }),
  }),
  specificGoals: z.string().min(10, "Please describe your goals (minimum 10 characters)"),
  successMetrics: z.string().optional(),
});

export type AIInterestsData = z.infer<typeof aiInterestsSchema>;

const aiSolutions = [
  "Customer Service Automation",
  "Sales Process Optimization",
  "Marketing Automation",
  "Data Analysis & Insights",
  "Content Creation",
  "Process Automation",
  "Predictive Analytics",
  "Chatbots & Virtual Assistants",
  "Document Processing",
  "Inventory Management",
  "Lead Generation",
  "Email Automation",
];

const usageLevels = [
  { value: "none", label: "No AI Experience", description: "New to AI technology" },
  { value: "basic", label: "Basic Usage", description: "Using simple AI tools occasionally" },
  { value: "intermediate", label: "Intermediate", description: "Regular use of AI tools" },
  { value: "advanced", label: "Advanced", description: "Extensive AI integration" },
];

const budgetRanges = [
  { value: "under-5k", label: "Under $5,000", description: "Small budget for AI tools" },
  { value: "5k-15k", label: "$5,000 - $15,000", description: "Moderate investment" },
  { value: "15k-50k", label: "$15,000 - $50,000", description: "Significant investment" },
  { value: "50k-100k", label: "$50,000 - $100,000", description: "Large scale implementation" },
  { value: "100k+", label: "$100,000+", description: "Enterprise level investment" },
];

const timelines = [
  { value: "immediate", label: "Immediate", description: "Start within 2 weeks" },
  { value: "1-3months", label: "1-3 Months", description: "Planning and gradual rollout" },
  { value: "3-6months", label: "3-6 Months", description: "Comprehensive implementation" },
  { value: "6months+", label: "6+ Months", description: "Long-term strategic approach" },
];

export function Phase2({ onNext, onBack, formData, setFormData }: Phase2Props) {
  const form = useZodForm({
    schema: aiInterestsSchema,
    initialValues: {
      aiInterests: formData?.aiInterests || [],
      currentAiUsage: formData?.currentAiUsage || ("" as any),
      budget: formData?.budget || ("" as any),
      timeline: formData?.timeline || ("" as any),
      specificGoals: formData?.specificGoals || "",
      successMetrics: formData?.successMetrics || "",
    },
    onSubmit: async (data) => {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Save form data and proceed to next phase
      setFormData(data);
      onNext();
    },
  });

  const handleAIInterestToggle = (interest: string) => {
    const currentInterests = form.values.aiInterests;
    const newInterests = currentInterests.includes(interest)
      ? currentInterests.filter((i) => i !== interest)
      : [...currentInterests, interest];
    
    form.setValue("aiInterests", newInterests);
  };

  return (
    <div className="w-full max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="space-y-8 sm:space-y-12">
        {/* Main Headline */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-title font-bold text-dark dark:text-white theme-transition leading-tight">
            AI Solutions & Investment
          </h1>
          <p className="text-lg sm:text-xl text-gray dark:text-gray theme-transition max-w-2xl mx-auto">
            Let's identify the AI solutions that will deliver the highest ROI for your business
          </p>
        </div>

        {/* Form Section */}
        <div className="max-w-2xl mx-auto">
          <form onSubmit={form.handleSubmit} className="space-y-6">
            {/* AI Interests */}
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-dark dark:text-light theme-transition">
                AI Solutions of Interest * (Select all that apply)
              </label>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {aiSolutions.map((solution) => (
                  <button
                    key={solution}
                    type="button"
                    onClick={() => handleAIInterestToggle(solution)}
                    className={`px-4 py-3 rounded-lg border-2 font-body transition-all duration-200 text-left ${
                      form.values.aiInterests.includes(solution)
                        ? "bg-dark dark:bg-light text-white dark:text-dark border-dark dark:border-light"
                        : "bg-white/80 dark:bg-gray/20 text-dark dark:text-white border-gray/30 dark:border-light/30 hover:border-dark dark:hover:border-light"
                    }`}
                  >
                    {solution}
                  </button>
                ))}
              </div>
              {form.errors.aiInterests && (
                <p className="text-red-500 text-sm font-body">{form.errors.aiInterests}</p>
              )}
            </div>

            {/* Current AI Usage */}
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-dark dark:text-light theme-transition">
                Current AI Usage Level *
              </label>
              <div className="space-y-3">
                {usageLevels.map((level) => (
                  <button
                    key={level.value}
                    type="button"
                    onClick={() => form.setValue("currentAiUsage", level.value as any)}
                    className={`w-full px-4 py-4 rounded-lg border-2 font-body transition-all duration-200 text-left ${
                      form.values.currentAiUsage === level.value
                        ? "bg-dark dark:bg-light text-white dark:text-dark border-dark dark:border-light"
                        : "bg-white/80 dark:bg-gray/20 text-dark dark:text-white border-gray/30 dark:border-light/30 hover:border-dark dark:hover:border-light"
                    }`}
                  >
                    <div className="font-semibold">{level.label}</div>
                    <div className="text-sm opacity-75">{level.description}</div>
                  </button>
                ))}
              </div>
              {form.errors.currentAiUsage && (
                <p className="text-red-500 text-sm font-body">{form.errors.currentAiUsage}</p>
              )}
            </div>

            {/* Budget */}
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-dark dark:text-light theme-transition">
                AI Investment Budget *
              </label>
              <div className="space-y-3">
                {budgetRanges.map((budget) => (
                  <button
                    key={budget.value}
                    type="button"
                    onClick={() => form.setValue("budget", budget.value as any)}
                    className={`w-full px-4 py-4 rounded-lg border-2 font-body transition-all duration-200 text-left ${
                      form.values.budget === budget.value
                        ? "bg-dark dark:bg-light text-white dark:text-dark border-dark dark:border-light"
                        : "bg-white/80 dark:bg-gray/20 text-dark dark:text-white border-gray/30 dark:border-light/30 hover:border-dark dark:hover:border-light"
                    }`}
                  >
                    <div className="font-semibold">{budget.label}</div>
                    <div className="text-sm opacity-75">{budget.description}</div>
                  </button>
                ))}
              </div>
              {form.errors.budget && (
                <p className="text-red-500 text-sm font-body">{form.errors.budget}</p>
              )}
            </div>

            {/* Timeline */}
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-dark dark:text-light theme-transition">
                Implementation Timeline *
              </label>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {timelines.map((timeline) => (
                  <button
                    key={timeline.value}
                    type="button"
                    onClick={() => form.setValue("timeline", timeline.value as any)}
                    className={`px-4 py-4 rounded-lg border-2 font-body transition-all duration-200 text-left ${
                      form.values.timeline === timeline.value
                        ? "bg-dark dark:bg-light text-white dark:text-dark border-dark dark:border-light"
                        : "bg-white/80 dark:bg-gray/20 text-dark dark:text-white border-gray/30 dark:border-light/30 hover:border-dark dark:hover:border-light"
                    }`}
                  >
                    <div className="font-semibold">{timeline.label}</div>
                    <div className="text-sm opacity-75">{timeline.description}</div>
                  </button>
                ))}
              </div>
              {form.errors.timeline && (
                <p className="text-red-500 text-sm font-body">{form.errors.timeline}</p>
              )}
            </div>

            {/* Specific Goals */}
            <div className="space-y-2">
              <label 
                htmlFor="specificGoals" 
                className="block text-sm font-semibold text-dark dark:text-light theme-transition"
              >
                Specific Goals *
              </label>
              <textarea
                id="specificGoals"
                value={form.values.specificGoals}
                onChange={(e) => form.setValue("specificGoals", e.target.value)}
                rows={4}
                className={`w-full px-4 py-3 rounded-lg border-2 bg-white/80 dark:bg-gray/20 text-dark dark:text-white font-body theme-transition focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 transition-all duration-200 resize-none ${
                  form.errors.specificGoals 
                    ? "border-red-500 focus:border-red-500" 
                    : "border-gray/30 dark:border-light/30 focus:border-dark dark:focus:border-light"
                }`}
                placeholder="What specific outcomes do you want to achieve with AI? (e.g., reduce response time by 50%, increase lead conversion by 30%)"
              />
              {form.errors.specificGoals && (
                <p className="text-red-500 text-sm font-body">{form.errors.specificGoals}</p>
              )}
            </div>

            {/* Success Metrics */}
            <div className="space-y-2">
              <label 
                htmlFor="successMetrics" 
                className="block text-sm font-semibold text-dark dark:text-light theme-transition"
              >
                Success Metrics (Optional)
              </label>
              <textarea
                id="successMetrics"
                value={form.values.successMetrics || ""}
                onChange={(e) => form.setValue("successMetrics", e.target.value)}
                rows={3}
                className="w-full px-4 py-3 rounded-lg border-2 bg-white/80 dark:bg-gray/20 text-dark dark:text-white font-body theme-transition focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 transition-all duration-200 border-gray/30 dark:border-light/30 focus:border-dark dark:focus:border-light resize-none"
                placeholder="How will you measure the success of AI implementation? (e.g., ROI, time saved, customer satisfaction scores)"
              />
            </div>

            {/* Navigation Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 pt-6">
              <button
                type="button"
                onClick={onBack}
                className="px-8 py-3 bg-white/10 dark:bg-gray/20 text-dark dark:text-light border-2 border-dark/20 dark:border-light/20 rounded-full font-body font-semibold transition-all duration-300 hover:bg-white/20 dark:hover:bg-gray/30 hover:border-dark/40 dark:hover:border-light/40 focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50"
              >
                Back
              </button>
              <button
                type="submit"
                disabled={form.isSubmitting}
                className={`flex-1 px-8 py-3 bg-dark dark:bg-light text-white dark:text-dark rounded-full font-body font-bold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl theme-transition focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 ${
                  form.isSubmitting ? 'hover:bg-dark dark:hover:bg-light' : 'hover:bg-gray-800 dark:hover:bg-gray-200'
                }`}
              >
                {form.isSubmitting ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    <span>Processing...</span>
                  </div>
                ) : (
                  "Generate My AI Audit"
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
