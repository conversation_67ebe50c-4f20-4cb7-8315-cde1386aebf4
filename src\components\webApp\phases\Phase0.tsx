import { z } from "zod";
import { useZodForm } from "@/hooks/useZodForm";

interface Phase0Props {
  onNext: () => void;
  formData?: WelcomeData;
  setFormData: (data: WelcomeData) => void;
}

const welcomeSchema = z.object({
  fullName: z
    .string()
    .min(1, "Full name is required")
    .min(2, "Full name must be at least 2 characters")
    .max(100, "Full name must be less than 100 characters"),
  companyName: z
    .string()
    .min(1, "Company name is required")
    .min(2, "Company name must be at least 2 characters")
    .max(100, "Company name must be less than 100 characters"),
  email: z
    .string()
    .min(1, "Email is required")
    .email({ message: "Please enter a valid email address" }),
  phone: z
    .string()
    .min(1, "Phone number is required")
    .regex(/^[+]?[1-9][\d\s\-()]{7,15}$/, "Please enter a valid phone number"),
  website: z
    .string()
    .optional()
    .refine(
      (val) => {
        if (!val || val.trim() === "") return true;

        const trimmedVal = val.trim();

        // Allow common URL formats:
        // - domain.com
        // - www.domain.com
        // - subdomain.domain.com
        // - https://domain.com
        // - http://www.domain.com
        // - domain.com/path
        const urlRegex =
          /^(https?:\/\/)?([\w-]+\.)*[\w-]+\.[a-zA-Z]{2,}(\/.*)?$/;

        return urlRegex.test(trimmedVal);
      },
      {
        message: "Please enter a valid website URL",
      }
    ),
});

export type WelcomeData = z.infer<typeof welcomeSchema>;

export function Phase0({ onNext, formData, setFormData }: Phase0Props) {
  const form = useZodForm({
    schema: welcomeSchema,
    initialValues: {
      fullName: formData?.fullName || "",
      companyName: formData?.companyName || "",
      email: formData?.email || "",
      phone: formData?.phone || "",
      website: formData?.website || "",
    },
    onSubmit: async (data) => {
      setFormData(data);
      onNext();
    },
  });

  return (
    <div className="w-full max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="space-y-8 sm:space-y-12">
        {/* Main Headline */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-title font-bold text-dark dark:text-white theme-transition leading-tight">
            Your competitors are using AI to get ahead.
          </h1>
        </div>

        {/* Subheading with emphasis */}
        <div className="text-center space-y-4 max-w-3xl mx-auto">
          <div className="text-lg sm:text-xl md:text-2xl font-body text-dark dark:text-white theme-transition leading-relaxed">
            <p className="mb-2">
              <strong className="font-bold">
                Stop leaving money on the table.
              </strong>
            </p>
            <p className="mb-2">
              Discover your business's top 3 automation opportunities in the
              next 90 seconds...
            </p>
            <p>
              <strong className="font-bold text-xl sm:text-2xl">
                FOR FREE
              </strong>
            </p>
          </div>
        </div>

        {/* Form Section */}
        <div className="max-w-2xl mx-auto">
          <form onSubmit={form.handleSubmit} className="space-y-6">
            {/* Full Name */}
            <div className="space-y-2">
              <label
                htmlFor="fullName"
                className="block text-sm font-semibold text-dark dark:text-light theme-transition"
              >
                Full Name *
              </label>
              <input
                type="text"
                id="fullName"
                value={form.values.fullName}
                onChange={(e) => form.setValue("fullName", e.target.value)}
                className={`w-full px-4 py-3 rounded-lg border-2 bg-white/80 dark:bg-gray/20 text-dark dark:text-white font-body theme-transition focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 transition-all duration-200 ${
                  form.errors.fullName
                    ? "border-red-500 focus:border-red-500"
                    : "border-gray/30 dark:border-light/30 focus:border-dark dark:focus:border-light"
                }`}
                placeholder="Enter your full name"
              />
              {form.errors.fullName && (
                <p className="text-red-500 text-sm font-body">
                  {form.errors.fullName}
                </p>
              )}
            </div>

            {/* Company Name */}
            <div className="space-y-2">
              <label
                htmlFor="companyName"
                className="block text-sm font-semibold text-dark dark:text-light theme-transition"
              >
                Company Name *
              </label>
              <input
                type="text"
                id="companyName"
                value={form.values.companyName}
                onChange={(e) => form.setValue("companyName", e.target.value)}
                className={`w-full px-4 py-3 rounded-lg border-2 bg-white/80 dark:bg-gray/20 text-dark dark:text-white font-body theme-transition focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 transition-all duration-200 ${
                  form.errors.companyName
                    ? "border-red-500 focus:border-red-500"
                    : "border-gray/30 dark:border-light/30 focus:border-dark dark:focus:border-light"
                }`}
                placeholder="Enter your company name"
              />
              {form.errors.companyName && (
                <p className="text-red-500 text-sm font-body">
                  {form.errors.companyName}
                </p>
              )}
            </div>

            {/* Email */}
            <div className="space-y-2">
              <label
                htmlFor="email"
                className="block text-sm font-semibold text-dark dark:text-light theme-transition"
              >
                Email *
              </label>
              <input
                type="email"
                id="email"
                value={form.values.email}
                onChange={(e) => form.setValue("email", e.target.value)}
                className={`w-full px-4 py-3 rounded-lg border-2 bg-white/80 dark:bg-gray/20 text-dark dark:text-white font-body theme-transition focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 transition-all duration-200 ${
                  form.errors.email
                    ? "border-red-500 focus:border-red-500"
                    : "border-gray/30 dark:border-light/30 focus:border-dark dark:focus:border-light"
                }`}
                placeholder="Enter your email address"
              />
              {form.errors.email && (
                <p className="text-red-500 text-sm font-body">
                  {form.errors.email}
                </p>
              )}
            </div>

            {/* Phone */}
            <div className="space-y-2">
              <label
                htmlFor="phone"
                className="block text-sm font-semibold text-dark dark:text-light theme-transition"
              >
                Phone Number *
              </label>
              <input
                type="tel"
                id="phone"
                value={form.values.phone}
                onChange={(e) => form.setValue("phone", e.target.value)}
                className={`w-full px-4 py-3 rounded-lg border-2 bg-white/80 dark:bg-gray/20 text-dark dark:text-white font-body theme-transition focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 transition-all duration-200 ${
                  form.errors.phone
                    ? "border-red-500 focus:border-red-500"
                    : "border-gray/30 dark:border-light/30 focus:border-dark dark:focus:border-light"
                }`}
                placeholder="Enter your phone number"
              />
              {form.errors.phone && (
                <p className="text-red-500 text-sm font-body">
                  {form.errors.phone}
                </p>
              )}
            </div>

            {/* Website */}
            <div className="space-y-2">
              <label
                htmlFor="website"
                className="block text-sm font-semibold text-dark dark:text-light theme-transition"
              >
                Company Website
              </label>
              <input
                type="text"
                id="website"
                value={form.values.website || ""}
                onChange={(e) => form.setValue("website", e.target.value)}
                className={`w-full px-4 py-3 rounded-lg border-2 bg-white/80 dark:bg-gray/20 text-dark dark:text-white font-body theme-transition focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 transition-all duration-200 ${
                  form.errors.website
                    ? "border-red-500 focus:border-red-500"
                    : "border-gray/30 dark:border-light/30 focus:border-dark dark:focus:border-light"
                }`}
                placeholder="https://yourcompany.com (optional)"
              />
              {form.errors.website && (
                <p className="text-red-500 text-sm font-body">
                  {form.errors.website}
                </p>
              )}
            </div>

            {/* Value Proposition Text */}
            <div className="text-center space-y-4 max-w-3xl mx-auto">
              <p className="text-base sm:text-lg md:text-xl font-body text-dark dark:text-white theme-transition leading-relaxed">
                Adeptos.ai has developed a proprietary analysis that instantly
                scans your business and pinpoints the exact areas where AI can
                deliver the highest impact on your bottom line and productivity.
              </p>
            </div>

            {/* Primary CTA Button */}
            <div className="text-center">
              <button
                type="submit"
                disabled={form.isSubmitting}
                className={`
                  px-8 sm:px-12 md:px-16 py-4 sm:py-5
                  text-lg sm:text-xl font-body font-bold
                  bg-dark dark:bg-light
                  text-white dark:text-dark
                  rounded-full
                  transition-all duration-300
                  hover:scale-105
                  shadow-lg hover:shadow-xl
                  theme-transition
                  backdrop-blur-sm
                  focus:outline-none
                  focus:ring-2
                  focus:ring-dark/50 dark:focus:ring-light/50
                  focus:ring-offset-2
                  focus:ring-offset-transparent
                  disabled:opacity-50
                  disabled:cursor-not-allowed
                  disabled:hover:scale-100
                  min-h-[56px] sm:min-h-[64px]
                  w-full max-w-md mx-auto
                  ${
                    form.isSubmitting
                      ? "hover:bg-dark dark:hover:bg-light"
                      : "hover:bg-gray-800 dark:hover:bg-gray-200"
                  }
                `}
                aria-label="Get your free AI business audit"
              >
                {form.isSubmitting ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    <span>Processing...</span>
                  </div>
                ) : (
                  "Get My Free Audit Now"
                )}
              </button>
            </div>
          </form>
        </div>

        {/* Privacy/Terms Notice */}
        <div className="text-center max-w-3xl mx-auto">
          <p className="text-xs sm:text-sm font-body text-gray dark:text-gray theme-transition leading-relaxed">
            By engaging this AI agent to audit your business you accept our
            privacy policy. You agree that our team may contact you with
            valuable information for your business.
          </p>
        </div>
      </div>
    </div>
  );
}
